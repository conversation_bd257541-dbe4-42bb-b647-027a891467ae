"""
AWS certification platform configuration.
"""

# AWS exam codes - comprehensive list of current AWS certifications
AWS_EXAM_CODES = [
    # Foundational
    "aws-certified-cloud-practitioner-clf-c02",
    
    # Associate Level
    "aws-certified-solutions-architect-associate-saa-c03",
    "aws-certified-developer-associate-dva-c02",
    "aws-certified-sysops-administrator-associate",
    "aws-certified-data-engineer-associate-dea-c01",
    "aws-certified-machine-learning-engineer-associate-mla-c01",
    
    # Professional Level
    "aws-certified-solutions-architect-professional-sap-c02",
    "aws-certified-devops-engineer-professional-dop-c02",
    
    # Specialty
    "aws-certified-advanced-networking-specialty-ans-c01",
    "aws-certified-security-specialty-scs-c02",
    "aws-certified-machine-learning-specialty",
    "aws-certified-ai-practitioner-aif-c01",
]

# AWS-specific special matching rules
AWS_SPECIAL_MATCHING_RULES = {
    "aws-certified-machine-learning-engineer-associate-mla-c01": {
        "alternative_patterns": [
            "aws-certified-machine-learning-engineer-associate-mla"
        ]
    }
}

# AWS platform configuration
AWS_CONFIG = {
    "platform_name": "aws",
    "provider_slug": "amazon",
    "exam_codes": AWS_EXAM_CODES,
    "special_matching_rules": AWS_SPECIAL_MATCHING_RULES,
    "description": "Amazon Web Services Certifications",
    "website": "https://aws.amazon.com/certification/"
}
