"""
GPT client utilities for AI processing.
"""

import os
import requests
import random
import time
import json
import sys
from datetime import datetime
from typing import List, Optional

def send_to_gpt(
    text: str,
    prompt: str,
    bearer_tokens: List[str] = [
        "sk-yOGPPghWJXYZs4mQ9hP4XKPz5EoSHZPCxH9kQk3W05G2JK1F",
        #"sk-lmab8Wl6AVU3VLKjsoGTP4H3zEK1CSV9HE1H7W7eOq3HXFPi"
    ],
    model: str = "gpt-4.1-mini",
    max_retries: int = 30,
    save_folder: str = "ai_responses"
) -> Optional[str]:
    # Define the absolute path for the API counter file
    api_counter_file = "D:\\python_scraping\\api_counter.txt"

    # Load existing counters
    if os.path.exists(api_counter_file):
        with open(api_counter_file, "r") as f:
            api_counters = json.load(f)
    else:
        api_counters = {}

    # Get current date
    today = datetime.now().strftime("%Y-%m-%d")

    # Initialize counters for today if not present
    if today not in api_counters:
        api_counters[today] = {}

    # Initialize a set to track tokens that hit the rate limit today
    rate_limited_tokens = set(api_counters.get(today, {}).get("rate_limited_tokens", []))

    error_data = {
        "system_prompt": text,
        "user_prompt": prompt,
        "model": model,
        "errors": []
    }

    json_payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": text},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7
    }

    if not bearer_tokens:
        print("🚨 Critical: No valid API tokens provided", file=sys.stderr)
        sys.exit(1)

    active_tokens = set(bearer_tokens) - rate_limited_tokens
    disabled_tokens = set()
    last_working_token = None

    for attempt in range(max_retries + 1):
        if not active_tokens:
            print("🔥 Catastrophic: All API tokens exhausted", file=sys.stderr)
            _print_debug_info(error_data)
            _save_error_payload(json_payload)
            sys.exit(1)

        # Smart token selection with fallback
        current_token = (
            last_working_token if last_working_token and last_working_token in active_tokens
            else random.choice(list(active_tokens))
        )
        token_suffix = current_token[-6:]

        response = None  # Initialize response to None
        try:
            response = requests.post(
                "https://api.chatanywhere.tech/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {current_token}",
                    "Content-Type": "application/json"
                },
                json=json_payload,
                timeout=60
            )
            response.raise_for_status()
            content = response.json().get("choices", [{}])[
                0].get("message", {}).get("content", "")
            if not content:
                print("⚠️ Warning: Empty response content")
                return None

            # Save the response to a text file
            os.makedirs(save_folder, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(
                save_folder, f"ai_response_{timestamp}.txt")
            with open(filename, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"AI response saved to '{filename}'")

            # Update last working token
            last_working_token = current_token

            # Update API counter
            try:
                ip_address = requests.get('https://api.ipify.org').text
                if current_token not in api_counters[today]:
                    api_counters[today][current_token] = []

                # Find if the IP already exists for this token
                ip_entry = next(
                    (entry for entry in api_counters[today][current_token] if entry["ip"] == ip_address), None)

                if ip_entry:
                    ip_entry["count"] += 1
                else:
                    api_counters[today][current_token].append(
                        {"count": 1, "ip": ip_address})

                # Save updated counters
                with open(api_counter_file, "w") as f:
                    json.dump(api_counters, f, indent=2)

                print('path:', api_counter_file)
                print(f"updated API counter: {ip_entry}")

            except Exception as counter_error:
                print(
                    f"Warning: Failed to update API counter: {counter_error}")

            return content

        except requests.exceptions.RequestException as e:
            # Initialize status_code and response_text
            status_code = None
            response_text = 'No response'

            if response:
                try:
                    status_code = response.status_code
                    response_text = response.text
                    print(f"Response status code: {status_code}")
                    print(f"Response text: {response_text[:200]}")
                except Exception as err:
                    print(f"Error accessing response attributes: {err}")
            else:
                print("Response is None")
                # Extract status code from exception message if possible
                if '429' in str(e):
                    status_code = 429
                elif '401' in str(e):
                    status_code = 401
                elif '403' in str(e):
                    status_code = 403

            # Track error details
            error_entry = {
                "attempt": attempt + 1,
                "token_suffix": token_suffix,
                "status": status_code,
                "error": str(e),
                "response": response_text[:500] + "..." if len(response_text) > 500 else response_text
            }
            print(f"⚠️ error_entry: {error_entry}")
            error_data["errors"].append(error_entry)

            # Token failure analysis
            if status_code in [401, 403]:
                print(
                    f"🔒 Authentication Failure (Code {status_code}) - Disabling token ...{token_suffix}")
                disabled_tokens.add(current_token)
                active_tokens.discard(current_token)
            elif status_code == 429:
                print(f"⏰ Rate Limit Hit - Disabling token ...{token_suffix}")
                disabled_tokens.add(current_token)
                active_tokens.discard(current_token)
                rate_limited_tokens.add(current_token)  # Add to rate-limited set
                api_counters[today]["rate_limited_tokens"] = list(rate_limited_tokens)  # Save rate-limited tokens
                with open(api_counter_file, "w") as f:
                    json.dump(api_counters, f, indent=2)
            else:
                print(
                    f"❌ Unexpected Error (Code {status_code}) - Retaining token ...{token_suffix}")

            # Final failure handling
            if attempt >= max_retries:
                print("\n🔥 Maximum retries exhausted. Error summary:",
                      file=sys.stderr)
                _print_debug_info(error_data)
                sys.exit(1)

            # Adaptive backoff with jitter
            sleep_time = random.uniform(0.5, 1.5) * (2 ** min(attempt//2, 5))
            print(
                f"⏱️  Retrying in {sleep_time:.1f}s (Active tokens: {len(active_tokens)})")
            time.sleep(sleep_time)

    return None


def _print_debug_info(error_data: dict):
    """Print structured error information for diagnostics"""
    print("\n=== ERROR DIAGNOSTICS ===")
    print(f"Model: {error_data['model']}")
    print(f"System Prompt: {error_data['system_prompt'][:200]}...")
    print(f"User Prompt: {error_data['user_prompt'][:200]}...")
    print("\nError Timeline:")
    for error in error_data["errors"]:
        print(f"Attempt {error['attempt']}:")
        print(f"  Token: ...{error['token_suffix']}")
        print(f"  Status: {error['status']}")
        print(f"  Error: {error['error']}")
        print(f"  Response: {error['response'][:200]}...")
    print("========================")


def _save_error_payload(payload: dict):
    """Save JSON payload to timestamped file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"gpt_error_payload_{timestamp}.json"

    try:
        with open(filename, 'w') as f:
            json.dump(payload, f, indent=2)
        print(f"Saved error payload to {filename}")
    except Exception as e:
        print(f"Failed to save error payload: {str(e)}")
