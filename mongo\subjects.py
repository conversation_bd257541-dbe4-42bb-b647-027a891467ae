"""
Multi-platform exam subjects extractor.

This module extracts exam subjects/domains from certification exam guides
using platform-specific processors (PDF for AWS, web scraping for Azure).
"""

from datetime import datetime
import os
import sys
import argparse
from typing import List, Dict, Optional
from pymongo import MongoClient
import pytz

# Import platform configurations and processors
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from platforms.aws.config import AWS_CONFIG
from platforms.azure.config import AZURE_CONFIG
from platforms.pmi.config import PMI_CONFIG
from platforms.aws.pdf_processor import process_aws_pdf, AWS_EXAM_PDF_URLS
from platforms.azure.web_scraper import process_azure_exam
from platforms.pmi.pdf_processor import process_pmi_pdf
from business_logic.subjects_business_logic import (
    get_exam_code_and_subjects_from_gpt,
    validate_exam_details,
    insert_log_subject_if_domains_differ,
    process_question_classification_workflow,
    update_exam_detail_and_qna_domains_with_gpt_domains
)

# Define Hong Kong timezone
tz = pytz.timezone("Asia/Hong_Kong")

# MongoDB connection setup
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
)
exam_db = client["exam"]
log_db = client["log"]
qna_db = client["qna"]

# Platform configurations
PLATFORM_CONFIGS = {
    'aws': {
        'config': AWS_CONFIG,
        'collection': 'amazon',
        'processor_type': 'pdf'
    },
    'azure': {
        'config': AZURE_CONFIG,
        'collection': 'microsoft',
        'processor_type': 'web_scraping'  # Future implementation
    },
    'pmi': {
        'config': PMI_CONFIG,
        'collection': 'pmi',
        'processor_type': 'pdf'
    }
}


def get_exam_codes_for_platform(platform: str) -> List[str]:
    """Get exam codes for a specific platform"""
    if platform not in PLATFORM_CONFIGS:
        print(f"🔥 Unknown platform: {platform}. Available: {list(PLATFORM_CONFIGS.keys())}")
        return []

    return PLATFORM_CONFIGS[platform]['config']['exam_codes']


def get_all_exam_codes() -> Dict[str, List[str]]:
    """Get exam codes for all platforms"""
    all_codes = {}
    for platform_name, platform_data in PLATFORM_CONFIGS.items():
        all_codes[platform_name] = platform_data['config']['exam_codes']
    return all_codes


def detect_platform_for_exam_code(exam_code: str) -> Optional[str]:
    """Detect which platform an exam code belongs to"""
    for platform_name, platform_data in PLATFORM_CONFIGS.items():
        if exam_code in platform_data['config']['exam_codes']:
            return platform_name
    return None


def process_aws_subjects(exam_codes: List[str] = None) -> None:
    """Process AWS exam subjects using PDF extraction with complete business logic"""
    if exam_codes is None:
        exam_codes = get_exam_codes_for_platform('aws')

    print(f"Processing AWS subjects for {len(exam_codes)} exams...")

    # For now, use the first PDF URL as a demonstration
    # In practice, you'd want to map specific exam codes to their PDF URLs
    pdf_url = AWS_EXAM_PDF_URLS[1]  # Cloud Practitioner as example

    print(f"Processing PDF: {pdf_url}")
    exam_code, domains = process_aws_pdf(pdf_url)

    if exam_code and domains:
        # Complete business logic workflow
        process_complete_subjects_workflow(exam_code, domains, pdf_url, platform="aws")
    else:
        print("Failed to process AWS PDF")


def process_pmi_subjects(exam_codes: List[str] = None) -> None:
    """Process PMI exam subjects using text file extraction with complete business logic"""
    if exam_codes is None:
        exam_codes = get_exam_codes_for_platform('pmi')

    print(f"Processing PMI subjects for {len(exam_codes)} exams...")

    for exam_code in exam_codes:
        print(f"\n🔄 Processing PMI exam: {exam_code}")

        # Process using exam code directly (no URL needed)
        processed_exam_code, domains = process_pmi_pdf(exam_code)

        if processed_exam_code and domains:
            # Complete business logic workflow
            process_complete_subjects_workflow(processed_exam_code, domains, None, platform="pmi")
            print(f"✅ Successfully processed {len(domains)} domains for {processed_exam_code}")
        else:
            print(f"❌ Failed to process PMI text file for {exam_code}")


def process_azure_subjects(exam_codes: List[str] = None) -> None:
    """Process Azure exam subjects using web scraping with complete business logic"""
    if exam_codes is None:
        exam_codes = get_exam_codes_for_platform('azure')

    print(f"Processing Azure subjects for {len(exam_codes)} exams...")

    # Process first 3 exams as demonstration (remove limit in production)
    for exam_code in exam_codes:
        print(f"\n🔄 Processing Azure exam: {exam_code}")

        # Use the Azure web scraper
        processed_code, domains = process_azure_exam(exam_code)

        if processed_code and domains:
            # Complete business logic workflow
            process_complete_subjects_workflow(processed_code, domains, None, platform="azure")
        else:
            print(f"❌ Failed to process Azure exam: {exam_code}")

def save_subjects_to_database(exam_code: str, domains: List[str]) -> None:
    """Save extracted subjects to database (legacy function for backward compatibility)"""
    try:
        # Create subjects document
        subjects_doc = {
            "exam_code": exam_code,
            "domains": domains,
            "timestamp_created": datetime.now(tz),
            "timestamp_updated": datetime.now(tz)
        }

        # Insert or update in database
        exam_db["subjects"].update_one(
            {"exam_code": exam_code},
            {"$set": subjects_doc},
            upsert=True
        )

        print(f"Saved subjects for {exam_code}: {len(domains)} domains")

    except Exception as e:
        print(f"Error saving subjects to database: {e}")


def process_complete_subjects_workflow(exam_code: str, domains: List[str], source_url: Optional[str], platform: str = "aws") -> None:
    """
    Complete subjects processing workflow with business logic integration.

    This function implements the complete workflow that was missing from the refactored version:
    1. Save subjects to database
    2. Validate GPT response against existing data
    3. Create/update log_subject record
    4. Process question classification workflow
    5. Update exam details and QNA domains

    Args:
        exam_code: Exam code
        domains: Extracted domains
        source_url: Source URL (PDF URL for AWS, web page for Azure)
        platform: Platform (aws or azure)
    """
    try:
        print(f"\n🔄 Starting complete subjects workflow for {exam_code} ({platform.upper()})")

        # Step 1: Save subjects to database (backward compatibility)
        save_subjects_to_database(exam_code, domains)

        # Step 2: Validate GPT response against existing data
        """ if validate_exam_details(exam_code, domains):
            print("✅ Subjects match existing data - updating final records")
            update_exam_detail_and_qna_domains_with_gpt_domains(exam_code, domains, platform)
            return """

        # Step 3: Create/update log_subject record if domains differ
        update_result = insert_log_subject_if_domains_differ(domains, exam_code)
        print(f"📝 Update log_subject result: {update_result}")

        # Step 4: Process question classification workflow
        # For this we need the cleaned text from the source
        if platform == "aws" and source_url:
            # For AWS, we need to re-extract the cleaned text from PDF
            from platforms.aws.pdf_processor import download_pdf, extract_text, clean_text

            print("🔄 Re-extracting cleaned text for question classification...")
            download_pdf(source_url, "temp_classification.pdf")
            extracted_text = extract_text("temp_classification.pdf")
            if extracted_text:
                cleaned_text = clean_text(extracted_text)
                if cleaned_text:
                    print("🤖 Starting question classification workflow...")
                    classification_success = process_question_classification_workflow(
                        exam_code, domains, cleaned_text, platform
                    )
                    if classification_success:
                        print("✅ Question classification completed successfully")
                    else:
                        print("❌ Question classification failed")
                else:
                    print("❌ Failed to clean extracted text")
            else:
                print("❌ Failed to extract text from PDF")

        elif platform == "azure":
            # For Azure, we'll use placeholder cleaned text for now
            #TODO In the future, this would use the actual scraped content
            placeholder_text = f"Azure certification exam {exam_code} domains: {', '.join(domains)}"
            print("🤖 Starting question classification workflow with placeholder text...")
            classification_success = process_question_classification_workflow(
                exam_code, domains, placeholder_text, platform
            )
            if classification_success:
                print("✅ Question classification completed successfully")
            else:
                print("❌ Question classification failed")

        elif platform == "pmi":
            # For PMI, read content from local text files
            from platforms.pmi.config import PMI_EXAM_TXT_PATHS
            from platforms.pmi.pdf_processor import read_text_file
            print(f"📖 Reading PMI {exam_code} exam content from file...")
            
            # Get file path from config
            file_path = PMI_EXAM_TXT_PATHS.get(exam_code.lower())
            if not file_path:
                print(f"❌ Error: No text file path configured for {exam_code}")
                return False
                
            # Read and clean text
            extracted_text = read_text_file(file_path)
            if not extracted_text:
                print("❌ Error: Failed to read PMI exam content file")
                return False
                
            cleaned_text = extracted_text.strip()
            
            print("🤖 Starting question classification workflow...")
            classification_success = process_question_classification_workflow(
                exam_code, domains, cleaned_text, platform
            )
            if classification_success:
                print("✅ Question classification completed successfully")
            else:
                print("❌ Question classification failed")

        # Step 5: Update exam details and QNA domains with final results
        print("🔄 Updating final exam details and QNA domains...")
        final_update_success = update_exam_detail_and_qna_domains_with_gpt_domains(exam_code, domains, platform)
        if final_update_success:
            print("✅ Final update completed successfully")
        else:
            print("❌ Final update failed")

        print(f"🎉 Complete subjects workflow finished for {exam_code}")

    except Exception as e:
        print(f"❌ Error in complete subjects workflow: {e}")


def main():
    """Main function with CLI argument parsing"""
    parser = argparse.ArgumentParser(
        description="Multi-platform exam subjects extractor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all AWS exams (default for backward compatibility)
  python subjects.py

  # Process all AWS exams explicitly
  python subjects.py --platform aws

  # Process all Azure exams
  python subjects.py --platform azure

  # Process specific exam codes
  python subjects.py --exam_codes aws-certified-cloud-practitioner-clf-c02 az-104
        """
    )

    parser.add_argument("--platform", choices=['aws', 'azure', 'pmi'], default=None,
                        help="Certification platform to process")
    parser.add_argument("--exam_codes", nargs="+", default=None,
                        help="List of specific exam codes to process")

    args = parser.parse_args()

    print("🚀 Starting multi-platform exam subjects extraction")

    # Determine which exam codes to process
    if args.exam_codes:
        # Process specific exam codes
        exam_codes_to_process = args.exam_codes
        print(f"📋 Processing specific exam codes: {len(exam_codes_to_process)}")
        print(f"🔍 Exam codes: {', '.join(exam_codes_to_process)}")

        # Group exam codes by platform
        platform_groups = {}
        for code in exam_codes_to_process:
            platform = detect_platform_for_exam_code(code)
            if platform:
                if platform not in platform_groups:
                    platform_groups[platform] = []
                platform_groups[platform].append(code)
                print(f"🎯 {code} -> {platform} platform")
            else:
                print(f"⚠️ Could not determine platform for exam code: {code}")

        # Process each platform group
        for platform, codes in platform_groups.items():
            print(f"\n🔥 Processing {platform.upper()} platform ({len(codes)} exams)")
            if platform == 'aws':
                process_aws_subjects(codes)
            elif platform == 'azure':
                process_azure_subjects(codes)
            elif platform == 'pmi':
                process_pmi_subjects(codes)

    elif args.platform:
        # Process all exams for specific platform
        print(f"📋 Processing all {args.platform.upper()} exams")

        if args.platform == 'aws':
            process_aws_subjects()
        elif args.platform == 'azure':
            process_azure_subjects()
        elif args.platform == 'pmi':
            process_pmi_subjects()

    else:
        # Default behavior for backward compatibility - process AWS
        print("📋 No platform specified, defaulting to AWS (backward compatibility)")
        process_aws_subjects()

    print("\n🎉 Exam subjects extraction completed!")


if __name__ == "__main__":
    main()